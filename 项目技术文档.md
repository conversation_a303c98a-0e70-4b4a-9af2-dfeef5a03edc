# 数字乡味项目技术文档

## 项目概述

**数字乡味**是一个传承方言文化的数字化平台，旨在通过菜谱分享、方言发音、文化故事等功能，保护和传承传统饮食文化。

### 核心功能
- 🍜 **菜谱管理**：用户可以创建、浏览、收藏菜谱
- 🎵 **方言发音**：支持上传菜名的方言音频
- 👥 **用户系统**：支持普通用户、传承人、管理员三种角色
- 💬 **互动功能**：评论、收藏、通知系统
- 📱 **响应式设计**：适配桌面端和移动端

## 技术基础知识（小白必读）

### 什么是前端、后端、数据库？

想象一个餐厅的运作模式：

🏪 **前端** = 餐厅的门面和服务员
- 就是用户看到的网页界面
- 负责展示信息、接收用户操作（点击、输入等）
- 就像餐厅的菜单、装修、服务员，用户直接接触的部分

🍳 **后端** = 餐厅的厨房和厨师
- 用户看不到，但负责处理所有业务逻辑
- 接收前端的请求，处理数据，返回结果
- 就像厨房里的厨师，根据订单制作菜品

📚 **数据库** = 餐厅的仓库
- 存储所有数据（用户信息、菜谱、评论等）
- 就像餐厅的食材仓库，存放所有原料

### 它们是如何协作的？

1. **用户操作**：用户在网页上点击"查看菜谱"
2. **前端处理**：前端接收点击，发送请求给后端
3. **后端处理**：后端接收请求，去数据库查找菜谱信息
4. **数据库响应**：数据库返回菜谱数据给后端
5. **后端返回**：后端处理数据，返回给前端
6. **前端展示**：前端接收数据，在页面上显示菜谱

### 为什么要这样分工？

- **安全性**：重要的业务逻辑在后端，用户无法直接修改
- **性能**：前端负责展示，后端负责计算，各司其职
- **维护性**：前端和后端可以独立开发和更新
- **扩展性**：可以为同一个后端开发多个前端（网页、手机APP等）

## 技术架构

### 整体架构
```
前端 (Vue 3 + Element Plus)
        ↕ HTTP API
后端 (Node.js + Express)
        ↕ ORM
数据库 (SQLite + Prisma)
        ↕ 文件存储
MinIO (对象存储)
```

### 数据流向示例
```
用户点击"登录"
    ↓
前端发送用户名密码到后端
    ↓
后端验证用户名密码（查询数据库）
    ↓
数据库返回用户信息
    ↓
后端生成登录令牌返回前端
    ↓
前端保存令牌，跳转到首页
```

### 技术栈详解

**前端技术栈（用户看到的界面）：**

🎨 **Vue 3** - 前端框架
- 作用：构建用户界面的工具
- 类比：就像搭积木的说明书，告诉我们如何组装网页
- 为什么选择：简单易学，性能好，社区活跃

🧭 **Vue Router 4** - 路由管理
- 作用：管理网页之间的跳转
- 类比：就像网站的导航系统，决定用户点击链接后去哪个页面
- 举例：从首页跳转到菜谱详情页

🗃️ **Pinia** - 状态管理
- 作用：管理整个应用的数据状态
- 类比：就像应用的"记忆"，记住用户是否登录、当前查看的菜谱等
- 举例：记住用户登录状态，在所有页面都能知道当前用户是谁

🎨 **Element Plus** - UI组件库
- 作用：提供现成的界面组件
- 类比：就像装修时的成品家具，不用自己做桌椅，直接用现成的
- 举例：按钮、表单、对话框等都是现成的组件

📡 **Axios** - HTTP客户端
- 作用：前端与后端通信的工具
- 类比：就像邮递员，负责在前端和后端之间传递消息
- 举例：发送登录请求、获取菜谱数据

⚡ **Vite** - 构建工具
- 作用：将开发时的代码打包成浏览器能理解的文件
- 类比：就像工厂的生产线，将原材料加工成成品
- 举例：将Vue代码转换成HTML、CSS、JavaScript文件

**后端技术栈（服务器端处理）：**

🟢 **Node.js** - 运行环境
- 作用：让JavaScript可以在服务器上运行
- 类比：就像给JavaScript提供了一个"家"，让它可以在服务器上工作
- 为什么选择：JavaScript前后端统一，开发效率高

🚀 **Express.js** - Web框架
- 作用：简化服务器开发，处理HTTP请求
- 类比：就像餐厅的服务流程标准，规定如何接待客人、处理订单
- 举例：定义API接口，处理用户请求

🔗 **Prisma** - ORM数据库工具
- 作用：简化数据库操作，提供类型安全
- 类比：就像数据库的"翻译官"，将复杂的数据库语言转换成简单的JavaScript
- 举例：`user.findMany()`就能查询所有用户，不用写复杂的SQL

💾 **SQLite** - 数据库
- 作用：存储应用的所有数据
- 类比：就像一个超级整理箱，按照规则存放所有信息
- 为什么选择：轻量级，无需单独安装数据库服务器

🔐 **JWT** - 身份认证
- 作用：验证用户身份，保护需要登录的功能
- 类比：就像身份证，证明你是谁，有什么权限
- 举例：用户登录后获得token，访问个人资料时需要验证token

📁 **MinIO** - 文件存储
- 作用：存储图片、音频等文件
- 类比：就像云盘，专门存放文件
- 举例：用户上传的菜谱图片、方言音频都存在这里

📤 **Multer** - 文件上传
- 作用：处理用户上传的文件
- 类比：就像快递接收点，接收用户发送的文件包裹
- 举例：用户上传菜谱图片时，Multer负责接收和处理

### 技术选型原因

**为什么选择这些技术？**

1. **学习成本低**：Vue 3相比React更容易上手
2. **开发效率高**：Element Plus提供丰富的组件，减少重复开发
3. **性能优秀**：Vite构建速度快，Prisma类型安全
4. **生态完善**：所有技术都有活跃的社区支持
5. **部署简单**：SQLite无需额外配置，Node.js部署方便

## 项目结构

### 根目录结构
```
test/
├── frontend/          # 前端Vue应用（用户看到的网页）
├── backend/           # 后端Node.js应用（服务器端逻辑）
└── 项目技术文档.md    # 本文档
```

### 前后端交互流程详解

让我们用一个具体例子来说明前后端是如何交互的：

**场景：用户查看菜谱详情**

1. **用户操作**
   ```
   用户在浏览器中点击"红烧肉"菜谱卡片
   ```

2. **前端处理**
   ```
   Vue Router: 跳转到菜谱详情页 (/recipes/123)
   Vue组件: RecipeDetailView.vue 开始加载
   Axios: 发送GET请求到 /api/recipes/123
   ```

3. **网络传输**
   ```
   HTTP请求通过互联网发送到后端服务器
   请求包含：URL、请求方法、用户认证信息等
   ```

4. **后端处理**
   ```
   Express: 接收请求，路由到 recipes.js
   中间件: 验证用户身份（如果需要）
   控制器: 调用 Prisma 查询数据库
   ```

5. **数据库查询**
   ```
   Prisma: 将JavaScript代码转换为SQL查询
   SQLite: 执行查询，返回菜谱数据
   包含：菜谱信息、作者信息、评论等
   ```

6. **后端响应**
   ```
   Express: 将数据库结果包装成JSON格式
   HTTP响应: 发送数据回前端
   ```

7. **前端展示**
   ```
   Axios: 接收后端响应
   Vue组件: 更新页面数据
   Element Plus: 渲染UI组件
   用户: 看到完整的菜谱详情页面
   ```

### 数据存储说明

**数据库存储（SQLite）**
- 用户信息：用户名、密码、角色等
- 菜谱信息：菜名、制作步骤、地区等
- 评论信息：评论内容、评分等
- 关系数据：用户收藏、菜谱分类等

**文件存储（MinIO）**
- 菜谱图片：JPG、PNG格式的图片文件
- 方言音频：MP3、WAV格式的音频文件
- 用户头像：用户上传的头像图片

**为什么要分开存储？**
- 数据库适合存储结构化数据（文字、数字）
- 文件存储适合存储非结构化数据（图片、音频）
- 分开存储可以提高性能和管理效率

## 后端详细说明

### 目录结构
```
backend/
├── server.js          # 服务器入口文件
├── package.json       # 项目依赖配置
├── prisma/            # 数据库相关
│   └── schema.prisma  # 数据库模型定义
├── routes/            # API路由
│   ├── auth.js        # 认证相关API
│   ├── recipes.js     # 菜谱相关API
│   ├── users.js       # 用户管理API
│   ├── comments.js    # 评论系统API
│   ├── favorites.js   # 收藏功能API
│   ├── upload.js      # 文件上传API
│   ├── applications.js # 传承人申请API
│   └── notifications.js # 通知系统API
├── middleware/        # 中间件
│   └── auth.js        # 身份认证中间件
├── lib/              # 工具库
│   ├── db.js         # 数据库连接
│   └── minio.js      # MinIO配置
├── scripts/          # 脚本文件
│   ├── seed.js       # 数据库种子数据
│   ├── seed_recipes.js # 菜谱种子数据
│   └── create-inheritor.js # 创建传承人脚本
└── uploads/          # 本地文件存储
    ├── images/       # 图片文件
    └── audio/        # 音频文件
```

### 核心文件说明

#### server.js - 服务器入口
- 配置Express应用
- 设置中间件（CORS、Helmet、Morgan等）
- 注册API路由
- 启动服务器和MinIO初始化

#### prisma/schema.prisma - 数据库模型
定义了以下数据模型：
- **User**: 用户表（支持USER、INHERITOR、ADMIN三种角色）
- **Recipe**: 菜谱表（包含方言名称、音频、文化信息等）
- **Category**: 分类表
- **Comment**: 评论表（支持回复功能）
- **UserFavorite**: 用户收藏关系表
- **InheritorApplication**: 传承人申请表
- **Notification**: 通知表

#### routes/ - API路由模块
每个路由文件负责特定功能的API接口：

**auth.js** - 认证系统
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- GET /api/auth/me - 获取当前用户信息

**recipes.js** - 菜谱管理
- GET /api/recipes - 获取菜谱列表（支持分页、搜索、筛选）
- POST /api/recipes - 创建菜谱
- GET /api/recipes/:id - 获取菜谱详情
- PUT /api/recipes/:id - 更新菜谱
- DELETE /api/recipes/:id - 删除菜谱

**users.js** - 用户管理
- GET /api/users - 获取用户列表（管理员）
- GET /api/users/:id - 获取用户详情
- PUT /api/users/:id - 更新用户信息
- DELETE /api/users/:id - 删除用户（管理员）

**comments.js** - 评论系统
- GET /api/comments/recipe/:recipeId - 获取菜谱评论
- POST /api/comments - 发表评论
- PUT /api/comments/:id - 更新评论
- DELETE /api/comments/:id - 删除评论

**favorites.js** - 收藏功能
- GET /api/favorites - 获取用户收藏列表
- POST /api/favorites - 添加收藏
- DELETE /api/favorites/:recipeId - 取消收藏

**upload.js** - 文件上传
- POST /api/upload/image - 上传图片
- POST /api/upload/audio - 上传音频

**applications.js** - 传承人申请
- POST /api/applications - 提交传承人申请
- GET /api/applications - 获取申请列表（管理员）
- PUT /api/applications/:id - 审核申请（管理员）

**notifications.js** - 通知系统
- GET /api/notifications - 获取用户通知
- PUT /api/notifications/:id/read - 标记通知已读

#### middleware/auth.js - 认证中间件
- **authenticateToken**: 验证JWT令牌
- **requireRole**: 角色权限检查
- **optionalAuth**: 可选认证（用于公开接口）

#### lib/ - 工具库
- **db.js**: Prisma客户端初始化
- **minio.js**: MinIO对象存储配置和工具函数

### 数据库设计详解

#### 什么是数据库？
数据库就像一个超级智能的文件柜，它可以：
- 存储大量数据
- 快速查找信息
- 保证数据不丢失
- 支持多人同时访问

#### 我们的数据库里存了什么？

**用户表（User）**
```
就像员工花名册，记录每个用户的信息：
- 用户ID（身份证号）
- 用户名（姓名）
- 密码（加密存储）
- 角色（普通用户/传承人/管理员）
- 头像、个人简介等
```

**菜谱表（Recipe）**
```
就像菜谱大全，记录每道菜的信息：
- 菜谱ID（菜谱编号）
- 菜名（如：红烧肉）
- 方言名称（如：红烧肉在某地方言中的叫法）
- 制作步骤、食材、地区等
- 作者ID（谁创建的这个菜谱）
```

**评论表（Comment）**
```
就像留言本，记录用户的评论：
- 评论ID（评论编号）
- 评论内容
- 评分（1-5星）
- 用户ID（谁发的评论）
- 菜谱ID（评论的是哪个菜谱）
```

**收藏表（UserFavorite）**
```
就像收藏夹，记录用户收藏了哪些菜谱：
- 用户ID（谁收藏的）
- 菜谱ID（收藏了哪个菜谱）
- 收藏时间
```

#### 用户角色系统
- **USER**: 普通用户，可以浏览、收藏、评论菜谱
- **INHERITOR**: 传承人，可以创建和管理菜谱
- **ADMIN**: 管理员，拥有所有权限

#### 核心业务流程
1. **用户注册/登录** → JWT认证（获得身份证明）
2. **传承人申请** → 管理员审核 → 角色升级
3. **菜谱创建** → 包含方言音频 → 发布
4. **用户互动** → 收藏、评论、通知

#### 数据关系举例
```
用户"张三"创建了菜谱"红烧肉"
用户"李四"收藏了"红烧肉"
用户"王五"评论了"红烧肉"："很好吃！"

在数据库中的关系：
- 菜谱表中"红烧肉"的作者ID = 张三的用户ID
- 收藏表中有一条记录：李四的用户ID + 红烧肉的菜谱ID
- 评论表中有一条记录：王五的用户ID + 红烧肉的菜谱ID + "很好吃！"
```

## 前端详细说明

### 目录结构
```
frontend/
├── index.html         # HTML入口文件
├── package.json       # 项目依赖配置
├── vite.config.js     # Vite构建配置
├── jsconfig.json      # JavaScript配置
├── public/            # 静态资源
│   └── image/         # 图片资源
│       └── bg.png     # 背景图片
└── src/               # 源代码
    ├── main.js        # 应用入口
    ├── App.vue        # 根组件
    ├── router/        # 路由配置
    ├── stores/        # 状态管理
    ├── api/           # API接口
    ├── components/    # 公共组件
    ├── views/         # 页面组件
    └── styles/        # 样式文件
```

### 核心文件说明

#### main.js - 应用入口
- 创建Vue应用实例
- 注册Element Plus组件库和图标
- 配置Pinia状态管理
- 配置Vue Router路由
- 挂载应用到DOM

#### App.vue - 根组件
- 应用的根组件
- 包含导航栏和路由视图
- 定义全局样式

#### router/index.js - 路由配置
定义了所有页面路由：
- `/` - 首页
- `/login` - 登录页
- `/register` - 注册页
- `/recipes` - 菜谱列表页
- `/recipes/:id` - 菜谱详情页
- `/create-recipe` - 创建菜谱页
- `/profile` - 个人资料页
- `/admin` - 管理员页面
- `/inheritor-dashboard` - 传承人仪表板

#### stores/user.js - 用户状态管理
使用Pinia管理用户状态：
- 用户登录状态
- 用户信息
- 登录/登出方法
- 权限检查方法

#### api/ - API接口模块
每个文件对应后端的一个路由模块：

**index.js** - Axios配置
- 创建axios实例
- 配置请求/响应拦截器
- 自动添加JWT token

**auth.js** - 认证接口
- login() - 用户登录
- register() - 用户注册
- getCurrentUser() - 获取当前用户

**recipes.js** - 菜谱接口
- getRecipes() - 获取菜谱列表
- getRecipe() - 获取菜谱详情
- createRecipe() - 创建菜谱
- updateRecipe() - 更新菜谱
- deleteRecipe() - 删除菜谱

**users.js** - 用户接口
- getUsers() - 获取用户列表
- getUser() - 获取用户详情
- updateUser() - 更新用户信息

**comments.js** - 评论接口
- getComments() - 获取评论列表
- createComment() - 发表评论
- updateComment() - 更新评论
- deleteComment() - 删除评论

**favorites.js** - 收藏接口
- getFavorites() - 获取收藏列表
- addFavorite() - 添加收藏
- removeFavorite() - 取消收藏

**upload.js** - 上传接口
- uploadImage() - 上传图片
- uploadAudio() - 上传音频

**applications.js** - 申请接口
- submitApplication() - 提交传承人申请
- getApplications() - 获取申请列表
- reviewApplication() - 审核申请

**notifications.js** - 通知接口
- getNotifications() - 获取通知列表
- markAsRead() - 标记已读

#### components/ - 公共组件

**NavBar.vue** - 导航栏组件
- 响应式导航菜单
- 用户登录状态显示
- 角色权限菜单

**CommentSection.vue** - 评论区组件
- 评论列表显示
- 发表评论功能
- 回复评论功能

**FavoriteButton.vue** - 收藏按钮组件
- 收藏/取消收藏功能
- 收藏状态显示

**NotificationBell.vue** - 通知铃铛组件
- 未读通知数量显示
- 通知下拉菜单

**NotificationCenter.vue** - 通知中心组件
- 通知列表显示
- 标记已读功能

#### views/ - 页面组件

**HomeView.vue** - 首页
- 英雄区域（数字乡味标题 + 背景图）
- 核心功能介绍
- 热门菜谱展示

**LoginView.vue** - 登录页
- 用户登录表单
- 表单验证
- 登录状态处理

**RegisterView.vue** - 注册页
- 用户注册表单
- 表单验证
- 注册成功处理

**RecipeListView.vue** - 菜谱列表页
- 菜谱卡片展示
- 搜索和筛选功能
- 分页功能

**RecipeDetailView.vue** - 菜谱详情页
- 菜谱详细信息展示
- 方言音频播放
- 评论区
- 收藏功能

**CreateRecipeView.vue** - 创建菜谱页
- 菜谱创建表单
- 图片和音频上传
- 表单验证

**EditRecipeView.vue** - 编辑菜谱页
- 菜谱编辑表单
- 数据预填充
- 更新功能

**ProfileView.vue** - 个人资料页
- 用户信息展示
- 个人菜谱列表
- 收藏菜谱列表

**AdminView.vue** - 管理员页面
- 用户管理
- 传承人申请审核
- 系统统计

**InheritorDashboard.vue** - 传承人仪表板
- 个人菜谱管理
- 创建菜谱快捷入口
- 数据统计

**InheritorProfileView.vue** - 传承人资料页
- 传承人详细信息
- 传承故事展示
- 作品展示

**NotificationView.vue** - 通知页面
- 通知列表
- 通知详情
- 批量操作

#### styles/variables.css - 全局样式变量
- CSS自定义属性定义
- 主题色彩配置
- Element Plus主题覆盖

## 开发环境配置

### 什么是开发环境？
开发环境就像工匠的工作台，包含了开发软件所需的所有工具：
- **代码编辑器**：写代码的地方（推荐VS Code）
- **运行环境**：让代码能够运行的基础软件
- **包管理器**：管理代码依赖的工具
- **版本控制**：记录代码变更历史的工具

### 环境要求
- **Node.js 20.19.0+ 或 22.12.0+**：JavaScript运行环境
- **npm 或 yarn**：包管理器（安装Node.js时会自带npm）
- **Git**：版本控制工具
- **VS Code**：代码编辑器（推荐）

### 详细安装教程（Windows系统）

#### 第一步：安装Node.js（必须）

**什么是Node.js？**
Node.js是让JavaScript可以在电脑上运行的环境，就像安装了一个"JavaScript解释器"。

**安装步骤：**
1. 打开浏览器，访问 https://nodejs.org
2. 你会看到两个下载按钮，选择左边的"LTS"版本（推荐版本）
3. 下载完成后，双击安装包
4. 安装向导中：
   - 点击"Next"继续
   - 勾选"I accept the terms"，点击"Next"
   - 安装路径保持默认，点击"Next"
   - 功能选择保持默认（全部勾选），点击"Next"
   - 点击"Install"开始安装
   - 等待安装完成，点击"Finish"

**验证安装是否成功：**
1. 按键盘上的 `Win + R` 键
2. 在弹出的对话框中输入 `cmd`，按回车
3. 在黑色的命令行窗口中输入：`node --version`
4. 如果显示类似 `v20.19.0` 的版本号，说明安装成功
5. 再输入：`npm --version`
6. 如果显示类似 `10.2.4` 的版本号，说明npm也安装成功

#### 第二步：安装Git（版本控制工具）

**什么是Git？**
Git是管理代码版本的工具，就像文档的"修订历史"功能，可以记录每次修改。

**安装步骤：**
1. 访问 https://git-scm.com
2. 点击"Download for Windows"
3. 下载完成后，双击安装包
4. 安装过程中的选择：
   - 许可协议：点击"Next"
   - 安装路径：保持默认，点击"Next"
   - 组件选择：保持默认，点击"Next"
   - 开始菜单文件夹：保持默认，点击"Next"
   - 默认编辑器：选择"Use Visual Studio Code"，点击"Next"
   - 其他选项都保持默认，一路点击"Next"
   - 最后点击"Install"

**验证Git安装：**
1. 重新打开命令行（cmd）
2. 输入：`git --version`
3. 如果显示版本号，说明安装成功

#### 第三步：安装VS Code（代码编辑器）

**什么是VS Code？**
VS Code是微软开发的免费代码编辑器，就像Word是写文档的，VS Code是写代码的。

**安装步骤：**
1. 访问 https://code.visualstudio.com
2. 点击"Download for Windows"
3. 下载完成后，双击安装包
4. 安装过程：
   - 接受许可协议，点击"下一步"
   - 安装路径保持默认，点击"下一步"
   - 勾选"添加到PATH"和"创建桌面图标"，点击"下一步"
   - 点击"安装"
   - 安装完成后，点击"完成"

**安装VS Code插件：**
1. 打开VS Code
2. 点击左侧的"扩展"图标（四个方块组成的图标）
3. 在搜索框中输入"Vue"，安装以下插件：
   - "Vue Language Features (Volar)" - Vue3支持
   - "TypeScript Vue Plugin (Volar)" - Vue类型支持
4. 搜索"Chinese"，安装"Chinese (Simplified)"插件，让VS Code显示中文

#### 第四步：安装MinIO（文件存储服务）

**什么是MinIO？**
MinIO是存储图片、音频文件的服务，就像一个专门的文件柜。

**Windows安装步骤：**
1. 访问 https://min.io/download
2. 选择"Windows"版本下载
3. 下载完成后，将文件重命名为 `minio.exe`
4. 在C盘创建一个文件夹，比如 `C:\minio`
5. 将 `minio.exe` 放到这个文件夹中
6. 在同一文件夹中创建一个 `data` 文件夹用于存储文件

**启动MinIO：**
1. 打开命令行（cmd）
2. 输入：`cd C:\minio`
3. 输入：`minio.exe server data`
4. 看到类似以下信息说明启动成功：
```
MinIO Object Storage Server
Copyright: 2015-2023 MinIO, Inc.
License: GNU AGPLv3 <https://www.gnu.org/licenses/agpl-3.0.html>
Version: RELEASE.2023-12-07T04-16-00Z

API: http://*************:9000  http://127.0.0.1:9000
Console: http://*************:9001 http://127.0.0.1:9001

Documentation: https://min.io/docs/minio/linux/index.html
Warning: The standard parity is set to 0. This can lead to data loss.
```

**配置MinIO（重要）：**
1. 打开浏览器，访问 http://localhost:9001
2. 默认用户名：`minioadmin`
3. 默认密码：`minioadmin`
4. 登录后，创建一个名为 `digital-hometown` 的存储桶（Bucket）

## 项目配置和启动教程

### 第一步：获取项目代码

**如果你有项目文件夹：**
1. 将项目文件夹复制到你的电脑上，比如 `D:\数字乡味项目`
2. 用VS Code打开这个文件夹：
   - 打开VS Code
   - 点击"文件" → "打开文件夹"
   - 选择项目文件夹

**如果你需要从Git下载：**
1. 打开命令行（cmd）
2. 切换到你想存放项目的目录，比如：`cd D:\`
3. 输入：`git clone [项目地址]`

### 第二步：配置后端环境

#### 2.1 创建环境配置文件

1. 在VS Code中，打开 `backend` 文件夹
2. 创建一个新文件，命名为 `.env`（注意前面有个点）
3. 在文件中输入以下内容：

```env
# 数据库配置
DATABASE_URL="file:./dev.db"

# JWT密钥（用于用户认证）
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# MinIO配置（文件存储）
MINIO_ENDPOINT="localhost"
MINIO_PORT=9000
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_USE_SSL=false
MINIO_BUCKET_NAME="digital-hometown"

# 服务器端口
PORT=3000
```

**重要说明：**
- `.env`文件存储敏感配置信息
- 不要将此文件分享给他人
- 生产环境中要修改JWT_SECRET为复杂密码

#### 2.2 安装后端依赖和配置数据库

**打开命令行并进入后端目录：**
1. 在VS Code中，按 `Ctrl + `` （反引号）打开终端
2. 或者按 `Win + R`，输入 `cmd`，然后用 `cd` 命令进入项目目录
3. 输入：`cd backend`

**安装依赖包：**
```bash
npm install
```
**这个命令会做什么？**
- 读取 `package.json` 文件
- 下载所有需要的第三方库（如Express、Prisma等）
- 可能需要几分钟时间，请耐心等待
- 完成后会看到 `node_modules` 文件夹

**生成数据库客户端：**
```bash
npm run db:generate
```
**这个命令会做什么？**
- Prisma读取 `schema.prisma` 文件
- 生成数据库操作的JavaScript代码
- 创建类型定义，让代码更安全

**创建数据库：**
```bash
npm run db:push
```
**这个命令会做什么？**
- 根据数据库模型创建SQLite数据库文件
- 文件位置：`backend/prisma/dev.db`
- 创建所有需要的数据表

**初始化测试数据：**
```bash
npm run seed
```
**这个命令会做什么？**
- 创建默认管理员账号：用户名 `admin`，密码 `admin123`
- 添加一些示例菜谱数据
- 创建基本的分类数据

**启动后端服务器：**
```bash
npm run dev
```
**这个命令会做什么？**
- 启动Express服务器
- 监听3000端口
- 自动重启（当代码修改时）
- 初始化MinIO连接

**成功启动的标志：**
```
🚀 数字乡味API服务已启动
📍 服务地址: http://localhost:3000
🌍 环境: development
📊 健康检查: http://localhost:3000/api/health
✅ MinIO初始化完成
```

### 第三步：配置前端环境

#### 3.1 安装前端依赖

**打开新的命令行窗口：**
1. 保持后端服务器运行
2. 打开新的命令行窗口（或VS Code新终端）
3. 进入前端目录：`cd frontend`

**安装前端依赖：**
```bash
npm install
```
**这个命令会做什么？**
- 下载Vue 3、Element Plus等前端库
- 下载Vite构建工具
- 创建 `node_modules` 文件夹

#### 3.2 启动前端开发服务器

```bash
npm run dev
```
**这个命令会做什么？**
- 启动Vite开发服务器
- 监听5173端口
- 支持热重载（代码修改后自动刷新页面）
- 编译Vue组件

**成功启动的标志：**
```
  VITE v7.0.6  ready in 1234 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
```

### 第四步：验证项目运行

#### 4.1 检查服务状态

**检查后端服务：**
1. 打开浏览器
2. 访问：http://localhost:3000/api/health
3. 如果看到类似以下内容，说明后端正常：
```json
{
  "status": "ok",
  "message": "数字乡味API服务运行正常",
  "timestamp": "2025-08-24T10:30:00.000Z"
}
```

**检查前端服务：**
1. 访问：http://localhost:5173
2. 如果看到"数字乡味"网站首页，说明前端正常

**检查MinIO服务：**
1. 访问：http://localhost:9001
2. 用户名：`minioadmin`，密码：`minioadmin`
3. 如果能登录看到管理界面，说明MinIO正常

#### 4.2 测试基本功能

**测试用户登录：**
1. 在网站首页点击"登录"
2. 输入用户名：`admin`
3. 输入密码：`admin123`
4. 如果能成功登录，说明数据库连接正常

**测试文件上传：**
1. 登录后，尝试创建菜谱
2. 上传一张图片
3. 如果上传成功，说明MinIO配置正常

### 第五步：常见配置问题解决

#### 问题1：端口被占用
```
Error: listen EADDRINUSE: address already in use :::3000
```
**解决方法：**
1. 找到占用端口的程序并关闭
2. 或者修改 `.env` 文件中的 `PORT=3001`
3. 重新启动后端服务

#### 问题2：数据库文件权限错误
```
Error: SQLITE_CANTOPEN: unable to open database file
```
**解决方法：**
1. 确保 `backend` 文件夹有写入权限
2. 删除 `backend/prisma/dev.db` 文件
3. 重新运行 `npm run db:push`

#### 问题3：MinIO连接失败
```
MinIO初始化失败: Connection refused
```
**解决方法：**
1. 确保MinIO服务正在运行
2. 检查 `.env` 文件中的MinIO配置
3. 确认端口9000和9001没有被其他程序占用

#### 问题4：前端页面空白
**解决方法：**
1. 按F12打开浏览器开发者工具
2. 查看Console标签页的错误信息
3. 确认后端服务是否正常运行
4. 检查网络请求是否成功

#### 问题5：依赖安装失败
```
npm ERR! network timeout
```
**解决方法：**
1. 检查网络连接
2. 使用国内镜像：
```bash
npm config set registry https://registry.npmmirror.com
```
3. 重新运行 `npm install`

## 完整的启动检查清单

### 启动前检查
- [ ] Node.js已安装（版本20+）
- [ ] Git已安装
- [ ] VS Code已安装
- [ ] MinIO已下载并配置
- [ ] 项目文件已获取

### 后端启动检查
- [ ] 已创建 `.env` 配置文件
- [ ] 已运行 `npm install`
- [ ] 已运行 `npm run db:generate`
- [ ] 已运行 `npm run db:push`
- [ ] 已运行 `npm run seed`
- [ ] MinIO服务正在运行
- [ ] 后端服务启动成功（端口3000）

### 前端启动检查
- [ ] 已运行 `npm install`
- [ ] 前端服务启动成功（端口5173）
- [ ] 可以访问网站首页

### 功能测试检查
- [ ] 健康检查接口正常（http://localhost:3000/api/health）
- [ ] 可以正常登录（admin/admin123）
- [ ] 可以浏览菜谱列表
- [ ] 可以上传图片（测试MinIO）

### 访问地址汇总
- **网站首页**：http://localhost:5173
- **后端API**：http://localhost:3000
- **健康检查**：http://localhost:3000/api/health
- **MinIO管理**：http://localhost:9001 （minioadmin/minioadmin）

## 日常开发工作流程

### 每天开始工作时
1. **启动MinIO**：
   ```bash
   cd C:\minio
   minio.exe server data
   ```

2. **启动后端**：
   ```bash
   cd backend
   npm run dev
   ```

3. **启动前端**：
   ```bash
   cd frontend
   npm run dev
   ```

### 修改代码后
- **前端代码修改**：页面会自动刷新，无需重启
- **后端代码修改**：服务器会自动重启，无需手动重启
- **数据库模型修改**：需要运行 `npm run db:push`

### 结束工作时
- 在命令行中按 `Ctrl + C` 停止服务
- 关闭VS Code
- MinIO可以保持运行，也可以关闭

## 部署说明

### 生产环境配置
1. **环境变量配置**（backend/.env）
```env
DATABASE_URL="file:./dev.db"
JWT_SECRET="your-jwt-secret"
MINIO_ENDPOINT="localhost"
MINIO_PORT=9000
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_USE_SSL=false
MINIO_BUCKET_NAME="digital-hometown"
```

2. **前端构建**
```bash
cd frontend
npm run build
```

3. **后端部署**
```bash
cd backend
npm install --production
npm run db:generate
npm run db:push
npm start
```

## 常见问题解答

### 开发问题

**问题1：数据库连接失败**
```
错误信息：Database connection failed
```
原因：数据库文件不存在或配置错误
解决步骤：
1. 检查backend/.env文件中的DATABASE_URL配置
2. 运行`npm run db:push`重新创建数据库
3. 确保backend目录有写入权限

**问题2：文件上传失败**
```
错误信息：Upload failed - MinIO connection error
```
原因：MinIO服务未启动或配置错误
解决步骤：
1. 检查MinIO是否正确安装和启动
2. 验证backend/.env中的MinIO配置参数
3. 确保MinIO端口（默认9000）未被占用

**问题3：前端API调用失败**
```
错误信息：Network Error 或 CORS error
```
原因：后端服务未启动或跨域配置问题
解决步骤：
1. 确认后端服务在3000端口正常运行
2. 检查server.js中的CORS配置
3. 确认前端请求的API地址正确

**问题4：页面显示空白**
```
现象：浏览器打开后页面是空白的
```
原因：前端构建失败或JavaScript错误
解决步骤：
1. 打开浏览器开发者工具（F12）查看错误信息
2. 检查前端服务是否正常启动
3. 确认所有依赖都已正确安装

**问题5：用户无法登录**
```
错误信息：Invalid credentials
```
原因：用户不存在或密码错误
解决步骤：
1. 确认是否已运行种子数据脚本（npm run seed）
2. 使用默认管理员账号：admin/admin123
3. 检查数据库中是否有用户数据

### 功能扩展建议
1. **搜索优化**：集成Elasticsearch提升搜索体验
2. **实时通信**：使用WebSocket实现实时聊天
3. **移动端**：开发React Native或Flutter移动应用
4. **AI功能**：集成语音识别和方言翻译
5. **社交功能**：添加用户关注、动态分享等功能

## 维护指南

### 数据库维护
- 定期备份SQLite数据库文件
- 监控数据库大小和性能
- 定期清理过期通知和日志

### 文件存储维护
- 定期清理未使用的上传文件
- 监控存储空间使用情况
- 配置文件备份策略

### 安全维护
- 定期更新依赖包
- 监控安全漏洞
- 定期轮换JWT密钥

## 给小白的学习建议

### 如果你想深入了解这个项目

**第一阶段：理解概念**
1. 了解什么是前端、后端、数据库
2. 理解HTTP请求和响应的概念
3. 学习基本的Web开发流程

**第二阶段：学习基础技术**
1. **HTML/CSS/JavaScript基础**
   - 推荐：MDN Web文档、菜鸟教程
2. **Vue.js基础**
   - 推荐：Vue官方文档、Vue Mastery
3. **Node.js基础**
   - 推荐：Node.js官方文档、慕课网

**第三阶段：实践项目**
1. 尝试修改现有功能（如改变页面样式）
2. 添加简单的新功能（如新增一个页面）
3. 学习使用开发者工具调试问题

### 推荐学习资源

**在线教程**
- Vue.js官方文档：https://cn.vuejs.org/
- Element Plus文档：https://element-plus.org/zh-CN/
- Node.js官方文档：https://nodejs.org/zh-cn/docs/
- Prisma文档：https://prisma.io/docs

**视频教程**
- B站搜索"Vue3入门教程"
- B站搜索"Node.js入门教程"
- 慕课网、极客时间等平台

**开发工具**
- VS Code：最受欢迎的代码编辑器
- Chrome DevTools：浏览器开发者工具
- Postman：API测试工具

### 项目维护注意事项

**日常维护**
1. 定期备份数据库文件（backend/prisma/dev.db）
2. 监控服务器运行状态
3. 及时处理用户反馈的问题

**安全注意事项**
1. 定期更新依赖包版本
2. 不要在代码中暴露敏感信息（密码、密钥等）
3. 定期检查系统日志

**性能优化**
1. 监控数据库大小，及时清理无用数据
2. 优化图片和音频文件大小
3. 定期检查服务器资源使用情况

---

**文档版本**: 2.0
**最后更新**: 2025-08-24
**维护人员**: 开发团队
**适用人群**: 技术小白、项目维护人员、新手开发者
