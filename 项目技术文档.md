# 数字乡味项目技术文档

## 项目概述

**数字乡味**是一个传承方言文化的数字化平台，旨在通过菜谱分享、方言发音、文化故事等功能，保护和传承传统饮食文化。

### 核心功能
- 🍜 **菜谱管理**：用户可以创建、浏览、收藏菜谱
- 🎵 **方言发音**：支持上传菜名的方言音频
- 👥 **用户系统**：支持普通用户、传承人、管理员三种角色
- 💬 **互动功能**：评论、收藏、通知系统
- 📱 **响应式设计**：适配桌面端和移动端

## 技术架构

### 整体架构
```
前端 (Vue 3 + Element Plus)
        ↕ HTTP API
后端 (Node.js + Express)
        ↕ ORM
数据库 (SQLite + Prisma)
        ↕ 文件存储
MinIO (对象存储)
```

### 技术栈

**前端技术栈：**
- Vue 3 (组合式API)
- Vue Router 4 (路由管理)
- Pinia (状态管理)
- Element Plus (UI组件库)
- Axios (HTTP客户端)
- Vite (构建工具)

**后端技术栈：**
- Node.js (运行环境)
- Express.js (Web框架)
- Prisma (ORM数据库工具)
- SQLite (数据库)
- JWT (身份认证)
- MinIO (文件存储)
- Multer (文件上传)

## 项目结构

### 根目录结构
```
test/
├── frontend/          # 前端Vue应用
├── backend/           # 后端Node.js应用
└── 项目技术文档.md    # 本文档
```

## 后端详细说明

### 目录结构
```
backend/
├── server.js          # 服务器入口文件
├── package.json       # 项目依赖配置
├── prisma/            # 数据库相关
│   └── schema.prisma  # 数据库模型定义
├── routes/            # API路由
│   ├── auth.js        # 认证相关API
│   ├── recipes.js     # 菜谱相关API
│   ├── users.js       # 用户管理API
│   ├── comments.js    # 评论系统API
│   ├── favorites.js   # 收藏功能API
│   ├── upload.js      # 文件上传API
│   ├── applications.js # 传承人申请API
│   └── notifications.js # 通知系统API
├── middleware/        # 中间件
│   └── auth.js        # 身份认证中间件
├── lib/              # 工具库
│   ├── db.js         # 数据库连接
│   └── minio.js      # MinIO配置
├── scripts/          # 脚本文件
│   ├── seed.js       # 数据库种子数据
│   ├── seed_recipes.js # 菜谱种子数据
│   └── create-inheritor.js # 创建传承人脚本
└── uploads/          # 本地文件存储
    ├── images/       # 图片文件
    └── audio/        # 音频文件
```

### 核心文件说明

#### server.js - 服务器入口
- 配置Express应用
- 设置中间件（CORS、Helmet、Morgan等）
- 注册API路由
- 启动服务器和MinIO初始化

#### prisma/schema.prisma - 数据库模型
定义了以下数据模型：
- **User**: 用户表（支持USER、INHERITOR、ADMIN三种角色）
- **Recipe**: 菜谱表（包含方言名称、音频、文化信息等）
- **Category**: 分类表
- **Comment**: 评论表（支持回复功能）
- **UserFavorite**: 用户收藏关系表
- **InheritorApplication**: 传承人申请表
- **Notification**: 通知表

#### routes/ - API路由模块
每个路由文件负责特定功能的API接口：

**auth.js** - 认证系统
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- GET /api/auth/me - 获取当前用户信息

**recipes.js** - 菜谱管理
- GET /api/recipes - 获取菜谱列表（支持分页、搜索、筛选）
- POST /api/recipes - 创建菜谱
- GET /api/recipes/:id - 获取菜谱详情
- PUT /api/recipes/:id - 更新菜谱
- DELETE /api/recipes/:id - 删除菜谱

**users.js** - 用户管理
- GET /api/users - 获取用户列表（管理员）
- GET /api/users/:id - 获取用户详情
- PUT /api/users/:id - 更新用户信息
- DELETE /api/users/:id - 删除用户（管理员）

**comments.js** - 评论系统
- GET /api/comments/recipe/:recipeId - 获取菜谱评论
- POST /api/comments - 发表评论
- PUT /api/comments/:id - 更新评论
- DELETE /api/comments/:id - 删除评论

**favorites.js** - 收藏功能
- GET /api/favorites - 获取用户收藏列表
- POST /api/favorites - 添加收藏
- DELETE /api/favorites/:recipeId - 取消收藏

**upload.js** - 文件上传
- POST /api/upload/image - 上传图片
- POST /api/upload/audio - 上传音频

**applications.js** - 传承人申请
- POST /api/applications - 提交传承人申请
- GET /api/applications - 获取申请列表（管理员）
- PUT /api/applications/:id - 审核申请（管理员）

**notifications.js** - 通知系统
- GET /api/notifications - 获取用户通知
- PUT /api/notifications/:id/read - 标记通知已读

#### middleware/auth.js - 认证中间件
- **authenticateToken**: 验证JWT令牌
- **requireRole**: 角色权限检查
- **optionalAuth**: 可选认证（用于公开接口）

#### lib/ - 工具库
- **db.js**: Prisma客户端初始化
- **minio.js**: MinIO对象存储配置和工具函数

### 数据库设计

#### 用户角色系统
- **USER**: 普通用户，可以浏览、收藏、评论菜谱
- **INHERITOR**: 传承人，可以创建和管理菜谱
- **ADMIN**: 管理员，拥有所有权限

#### 核心业务流程
1. **用户注册/登录** → JWT认证
2. **传承人申请** → 管理员审核 → 角色升级
3. **菜谱创建** → 包含方言音频 → 发布
4. **用户互动** → 收藏、评论、通知

## 前端详细说明

### 目录结构
```
frontend/
├── index.html         # HTML入口文件
├── package.json       # 项目依赖配置
├── vite.config.js     # Vite构建配置
├── jsconfig.json      # JavaScript配置
├── public/            # 静态资源
│   └── image/         # 图片资源
│       └── bg.png     # 背景图片
└── src/               # 源代码
    ├── main.js        # 应用入口
    ├── App.vue        # 根组件
    ├── router/        # 路由配置
    ├── stores/        # 状态管理
    ├── api/           # API接口
    ├── components/    # 公共组件
    ├── views/         # 页面组件
    └── styles/        # 样式文件
```

### 核心文件说明

#### main.js - 应用入口
- 创建Vue应用实例
- 注册Element Plus组件库和图标
- 配置Pinia状态管理
- 配置Vue Router路由
- 挂载应用到DOM

#### App.vue - 根组件
- 应用的根组件
- 包含导航栏和路由视图
- 定义全局样式

#### router/index.js - 路由配置
定义了所有页面路由：
- `/` - 首页
- `/login` - 登录页
- `/register` - 注册页
- `/recipes` - 菜谱列表页
- `/recipes/:id` - 菜谱详情页
- `/create-recipe` - 创建菜谱页
- `/profile` - 个人资料页
- `/admin` - 管理员页面
- `/inheritor-dashboard` - 传承人仪表板

#### stores/user.js - 用户状态管理
使用Pinia管理用户状态：
- 用户登录状态
- 用户信息
- 登录/登出方法
- 权限检查方法

#### api/ - API接口模块
每个文件对应后端的一个路由模块：

**index.js** - Axios配置
- 创建axios实例
- 配置请求/响应拦截器
- 自动添加JWT token

**auth.js** - 认证接口
- login() - 用户登录
- register() - 用户注册
- getCurrentUser() - 获取当前用户

**recipes.js** - 菜谱接口
- getRecipes() - 获取菜谱列表
- getRecipe() - 获取菜谱详情
- createRecipe() - 创建菜谱
- updateRecipe() - 更新菜谱
- deleteRecipe() - 删除菜谱

**users.js** - 用户接口
- getUsers() - 获取用户列表
- getUser() - 获取用户详情
- updateUser() - 更新用户信息

**comments.js** - 评论接口
- getComments() - 获取评论列表
- createComment() - 发表评论
- updateComment() - 更新评论
- deleteComment() - 删除评论

**favorites.js** - 收藏接口
- getFavorites() - 获取收藏列表
- addFavorite() - 添加收藏
- removeFavorite() - 取消收藏

**upload.js** - 上传接口
- uploadImage() - 上传图片
- uploadAudio() - 上传音频

**applications.js** - 申请接口
- submitApplication() - 提交传承人申请
- getApplications() - 获取申请列表
- reviewApplication() - 审核申请

**notifications.js** - 通知接口
- getNotifications() - 获取通知列表
- markAsRead() - 标记已读

#### components/ - 公共组件

**NavBar.vue** - 导航栏组件
- 响应式导航菜单
- 用户登录状态显示
- 角色权限菜单

**CommentSection.vue** - 评论区组件
- 评论列表显示
- 发表评论功能
- 回复评论功能

**FavoriteButton.vue** - 收藏按钮组件
- 收藏/取消收藏功能
- 收藏状态显示

**NotificationBell.vue** - 通知铃铛组件
- 未读通知数量显示
- 通知下拉菜单

**NotificationCenter.vue** - 通知中心组件
- 通知列表显示
- 标记已读功能

#### views/ - 页面组件

**HomeView.vue** - 首页
- 英雄区域（数字乡味标题 + 背景图）
- 核心功能介绍
- 热门菜谱展示

**LoginView.vue** - 登录页
- 用户登录表单
- 表单验证
- 登录状态处理

**RegisterView.vue** - 注册页
- 用户注册表单
- 表单验证
- 注册成功处理

**RecipeListView.vue** - 菜谱列表页
- 菜谱卡片展示
- 搜索和筛选功能
- 分页功能

**RecipeDetailView.vue** - 菜谱详情页
- 菜谱详细信息展示
- 方言音频播放
- 评论区
- 收藏功能

**CreateRecipeView.vue** - 创建菜谱页
- 菜谱创建表单
- 图片和音频上传
- 表单验证

**EditRecipeView.vue** - 编辑菜谱页
- 菜谱编辑表单
- 数据预填充
- 更新功能

**ProfileView.vue** - 个人资料页
- 用户信息展示
- 个人菜谱列表
- 收藏菜谱列表

**AdminView.vue** - 管理员页面
- 用户管理
- 传承人申请审核
- 系统统计

**InheritorDashboard.vue** - 传承人仪表板
- 个人菜谱管理
- 创建菜谱快捷入口
- 数据统计

**InheritorProfileView.vue** - 传承人资料页
- 传承人详细信息
- 传承故事展示
- 作品展示

**NotificationView.vue** - 通知页面
- 通知列表
- 通知详情
- 批量操作

#### styles/variables.css - 全局样式变量
- CSS自定义属性定义
- 主题色彩配置
- Element Plus主题覆盖

## 开发环境配置

### 环境要求
- Node.js 20.19.0+ 或 22.12.0+
- npm 或 yarn
- Git

### 后端启动步骤
```bash
cd backend
npm install                    # 安装依赖
npm run db:generate            # 生成Prisma客户端
npm run db:push               # 推送数据库模式
npm run seed                  # 初始化种子数据
npm run dev                   # 启动开发服务器
```

### 前端启动步骤
```bash
cd frontend
npm install                   # 安装依赖
npm run dev                  # 启动开发服务器
```

### 访问地址
- 前端：http://localhost:5173
- 后端API：http://localhost:3000
- 健康检查：http://localhost:3000/api/health

## 部署说明

### 生产环境配置
1. **环境变量配置**（backend/.env）
```env
DATABASE_URL="file:./dev.db"
JWT_SECRET="your-jwt-secret"
MINIO_ENDPOINT="localhost"
MINIO_PORT=9000
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_USE_SSL=false
MINIO_BUCKET_NAME="digital-hometown"
```

2. **前端构建**
```bash
cd frontend
npm run build
```

3. **后端部署**
```bash
cd backend
npm install --production
npm run db:generate
npm run db:push
npm start
```

## 常见问题

### 开发问题
1. **数据库连接失败**
   - 检查DATABASE_URL配置
   - 运行`npm run db:push`重新同步

2. **文件上传失败**
   - 检查MinIO服务是否启动
   - 验证MinIO配置参数

3. **前端API调用失败**
   - 检查后端服务是否启动
   - 验证CORS配置

### 功能扩展建议
1. **搜索优化**：集成Elasticsearch提升搜索体验
2. **实时通信**：使用WebSocket实现实时聊天
3. **移动端**：开发React Native或Flutter移动应用
4. **AI功能**：集成语音识别和方言翻译
5. **社交功能**：添加用户关注、动态分享等功能

## 维护指南

### 数据库维护
- 定期备份SQLite数据库文件
- 监控数据库大小和性能
- 定期清理过期通知和日志

### 文件存储维护
- 定期清理未使用的上传文件
- 监控存储空间使用情况
- 配置文件备份策略

### 安全维护
- 定期更新依赖包
- 监控安全漏洞
- 定期轮换JWT密钥

---

**文档版本**: 1.0
**最后更新**: 2025-08-24
**维护人员**: 开发团队
