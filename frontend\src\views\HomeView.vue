<template>
  <div class="home">
    <!-- 英雄区域 -->
    <section class="hero">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            <span class="title-main">数字乡味</span>
            <span class="title-sub">传承方言文化，品味家乡记忆</span>
          </h1>
          <div class="hero-actions">
            <el-button type="primary" size="large" class="btn-explore" @click="$router.push('/recipes')">
              探索菜谱库
            </el-button>
            <el-button size="large" class="btn-story" @click="scrollToFeatures">
              方言故事
            </el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- 特色功能 -->
    <section class="features" ref="featuresRef">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">核心功能</h2>
          <p class="section-subtitle">传承文化，连接心灵的四大核心能力</p>
        </div>
        <div class="features-grid">
          <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
            <div class="feature-icon microphone">
              <el-icon><Microphone /></el-icon>
            </div>
            <h3>方言发音</h3>
            <p>标准的方言发音录制，让您听到最地道的家乡味道</p>
            <div class="feature-decoration"></div>
          </div>
          <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
            <div class="feature-icon document">
              <el-icon><Document /></el-icon>
            </div>
            <h3>菜谱教学</h3>
            <p>详细的制作步骤，传统工艺的完整记录和传承</p>
            <div class="feature-decoration"></div>
          </div>
          <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
            <div class="feature-icon star">
              <el-icon><Star /></el-icon>
            </div>
            <h3>文化故事</h3>
            <p>每道菜背后的历史渊源和文化内涵深度挖掘</p>
            <div class="feature-decoration"></div>
          </div>
          <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
            <div class="feature-icon chat">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <h3>互动社区</h3>
            <p>与同乡人交流分享，共同传承和发扬家乡文化</p>
            <div class="feature-decoration"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门菜谱 -->
    <section class="popular-recipes">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">热门菜谱</h2>
          <p class="section-subtitle">传承人精心制作，最受欢迎的传统美食</p>
        </div>
        <div class="recipes-grid" v-loading="loading">
          <div
            v-for="(recipe, index) in popularRecipes"
            :key="recipe.id"
            class="recipe-card"
            :data-aos="'fade-up'"
            :data-aos-delay="index * 100"
            @click="$router.push(`/recipes/${recipe.id}`)"
          >
            <div class="recipe-image">
              <img :src="recipe.image || '/placeholder-dish.jpg'" :alt="recipe.name" />
              <div class="recipe-overlay">
                <div class="recipe-badge" v-if="recipe.author?.role === 'INHERITOR'">
                  <span>传承人作品</span>
                </div>
              </div>
            </div>
            <div class="recipe-info">
              <h3 class="recipe-name">{{ recipe.name }}</h3>
              <p class="recipe-dialect">{{ recipe.dialectName }}</p>
              <div class="recipe-meta">
                <span class="recipe-region">
                  <el-icon><Location /></el-icon>
                  {{ recipe.region }}
                </span>
                <span class="recipe-author" v-if="recipe.author?.role === 'INHERITOR'">
                  <el-icon><User /></el-icon>
                  {{ recipe.author.username }}
                </span>
              </div>
              <div class="recipe-stats">
                <span class="stat-item">
                  <el-icon><View /></el-icon>
                  {{ recipe.viewCount }}
                </span>
                <span class="stat-item">
                  <el-icon><Star /></el-icon>
                  {{ recipe._count?.favorites || 0 }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="view-more">
          <el-button type="primary" size="large" class="btn-more" @click="$router.push('/recipes')">
            查看更多菜谱
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getRecipes } from '../api/recipes.js'

const featuresRef = ref(null)
const popularRecipes = ref([])
const loading = ref(false)

const scrollToFeatures = () => {
  featuresRef.value?.scrollIntoView({ behavior: 'smooth' })
}

const fetchPopularRecipes = async () => {
  try {
    loading.value = true
    const response = await getRecipes({ limit: 6 })
    popularRecipes.value = response.recipes || []
  } catch (error) {
    console.error('获取热门菜谱失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchPopularRecipes()
})
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: #fafafa;
}

/* 英雄区域 */
.hero {
  background: url('/image/bg.png') center/cover no-repeat;
  min-height: 80vh;
  display: flex;
  align-items: center;
  color: white;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.hero-text {
  text-align: center;
}


.pixel-bowl {
  font-size: 4rem;
  margin: 20px 0;
}

.hero-title {
  margin-bottom: 700px;
}

.title-main {
  display: block;
  font-size: 6rem;
  font-weight: bold;
  margin-bottom: 12px;
  color: white;
  line-height: 1.1;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 2px;
  font-family: "KaiTi", "楷体", "STKaiti", serif;
}

.title-sub {
  display: block;
  font-size: 1.2rem;
  font-weight: 400;
  color: #d9c688;
  letter-spacing: 2px;
  font-style: italic;
}



.hero-actions {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 40px;
}

.btn-explore {
  background: #245196;
  border: 2px solid #cfc083;
  color: #f1eab0;
  padding: 24px 24px;
  font-size: 2rem;
  font-weight: 400;
  border-radius: 25px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-family: "KaiTi", "楷体", "STKaiti", serif;
}

.btn-explore:hover {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.btn-story {
  background: #faf4e6;
  border: 2px solid #cdbc7b;
  color: #cdb471;
  padding: 24px 24px;
  font-size: 2rem;
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  font-family: "KaiTi", "楷体", "STKaiti", serif;
}

.btn-story:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}



/* 特色功能 */
.features {
  padding: 100px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--brand-secondary), transparent);
}



.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.8rem;
  color: #2c3e50;
  margin-bottom: 15px;
  font-weight: 700;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-1));
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #6c757d;
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

/* 当屏幕较小时，改为2x2布局 */
@media (max-width: 1200px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.feature-card {
  position: relative;
  text-align: center;
  padding: 40px 30px;
  border-radius: var(--el-border-radius-base);
  background: white;
  box-shadow: var(--el-box-shadow);
  transition: all 0.4s ease;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-1));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--el-box-shadow-dark);
}

.feature-decoration {
  position: absolute;
  bottom: -20px;
  right: -20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
  border-radius: 50%;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-decoration {
  transform: scale(1.2);
  opacity: 0.8;
}

.feature-icon {
  font-size: 3.5rem;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.feature-icon.microphone {
  color: var(--el-color-primary-dark-2);
}

.feature-icon.document {
  color: var(--el-color-primary);
}

.feature-icon.star {
  color: var(--brand-secondary);
}

.feature-icon.chat {
  color: var(--el-color-primary-light-1);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 15px;
  font-weight: 600;
}

.feature-card p {
  color: #6c757d;
  line-height: 1.7;
  font-size: 1rem;
}

/* 热门菜谱 */
.popular-recipes {
  padding: 100px 20px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.recipe-card {
  background: white;
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  box-shadow: var(--el-box-shadow);
  transition: all 0.4s ease;
  cursor: pointer;
  position: relative;
}

.recipe-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--el-box-shadow-dark);
}

.recipe-image {
  height: 220px;
  overflow: hidden;
  position: relative;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.recipe-card:hover .recipe-image img {
  transform: scale(1.05);
}

.recipe-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 15px;
}

.recipe-badge {
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-1));
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: var(--el-box-shadow);
}

.recipe-info {
  padding: 25px;
}

.recipe-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
  line-height: 1.3;
}

.recipe-dialect {
  font-size: 1.1rem;
  color: var(--el-color-primary);
  margin-bottom: 15px;
  font-weight: 500;
}

.recipe-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.recipe-region,
.recipe-author {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #6c757d;
  font-size: 0.9rem;
}

.recipe-stats {
  display: flex;
  gap: 20px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #6c757d;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.recipe-card:hover .stat-item {
  color: var(--el-color-primary);
}

.view-more {
  text-align: center;
}

.btn-more {
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-1));
  border: none;
  padding: 15px 40px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  box-shadow: var(--el-box-shadow);
  transition: all 0.3s ease;
}

.btn-more:hover {
  transform: translateY(-2px);
  box-shadow: var(--el-box-shadow-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero {
    padding: 80px 20px;
    min-height: 70vh;
  }

  .hero-content {
    max-width: 100%;
  }

  .title-main {
    font-size: 3rem;
  }

  .section-title {
    font-size: 2.2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .recipes-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .btn-explore,
  .btn-story {
    width: 100%;
    max-width: 280px;
  }

  .feature-card {
    padding: 30px 20px;
  }

  .recipe-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
